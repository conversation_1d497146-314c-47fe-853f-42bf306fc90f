.container {
  background-color: #fff;
  overflow: hidden;

  & + & {
    margin-top: 10px;
  }

  .header {
    display: flex;
    align-items: center;
    background: #f2f9fc;
    border: 1px solid #e4eef6;

    &.clickable {
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background: #eaf8ff;
      }
    }

    &:hover {
      .expandIcon {
        color: #128bed;
      }
    }
  }

  .expandIcon {
    font-size: 16px;
    color: #666;
    padding: 0 10px;
    // transition:
    //   transform 0.2s ease,
    //   color 0.2s ease;
  }

  .headerContent {
    flex: 1;
    display: flex;
    align-items: center;
  }

  .body {
    transition: all 0.3s ease;
    overflow: hidden;

    &.collapsed {
      max-height: 0;
      opacity: 0;
    }
  }
}

.checkbox {
  width: 34px;
  border-right: 1px solid #E4EEF6;
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: stretch;
}
