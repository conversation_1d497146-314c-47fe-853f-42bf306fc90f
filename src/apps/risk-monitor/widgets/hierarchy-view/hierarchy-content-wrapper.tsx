import { defineComponent, inject } from 'vue';
import styles from './hierarchy-content-wrapper.module.less';

const HierarchyContentWrapper = defineComponent({
  name: 'HierarchyContentWrapper',
  props: {},
  setup() {
    const selectable = inject('selectable');
    return {
      selectable,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        {this.selectable ? <div class={styles.space}></div> : null}
        <div class={styles.content}>{this.$slots.default}</div>
      </div>
    );
  },
});

export default HierarchyContentWrapper;
