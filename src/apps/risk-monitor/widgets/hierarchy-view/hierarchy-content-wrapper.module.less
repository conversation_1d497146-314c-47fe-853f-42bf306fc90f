.container {
  display: flex;
}

.space {
  width: 34px;
  border-color: #e4eef6;
  border-width: 0px 0px 1px 1px;
  border-style: solid;
}

.content {
  flex: 1;
  border-color: #e4eef6;
  border-width: 0px 1px 1px 1px;
  border-style: solid;
  position: relative;
  padding-left: 36px;

  &::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #d8d8d8;
    position: absolute;
    top: 20px;
    left: 16px;
  }

  &::after {
    content: '';
    width: 1px;
    background: #d8d8d8;
    position: absolute;
    top: 32px;
    left: 18px;
    bottom: 10px;
  }
}
