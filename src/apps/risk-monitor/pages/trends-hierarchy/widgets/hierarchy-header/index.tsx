import CompanyLogo from '@/components/company-logo';
import { computed, defineComponent } from 'vue';
import { escape } from 'lodash';
import styles from './hierarchy-header.module.less';
import QIcon from '@/components/global/q-icon';

const HierarchyHeader = defineComponent({
  name: 'HierarchyHeader',
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
    searchKey: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const highlightName = computed(() => {
      const companyName = escape(props.item.companyName).replace(props.searchKey, `<em style="color: #f04040">${props.searchKey}</em>`);
      return companyName;
    });
    return {
      highlightName,
    };
  },
  render() {
    const { item, highlightName } = this;
    return (
      <div class={styles.container}>
        <div class={styles.companyInfo}>
          <CompanyLogo src={item.ImageUrl} id={item.companyId} name={item.ShortName || item.companyName} hasimage={item.HasImage} />
          <span data-testid="company-name" class="mr-1" domPropsInnerHTML={highlightName} />
        </div>
        <ul>
          <li>
            <QIcon type="icon-role-main" />
            <span>主体企业动态</span>
            <span>{item.count}</span>
          </li>
          <li>
            <QIcon type="icon-role-sub" />
            <span>关联方企业</span>
            <span>{item.count2}，</span>
            <span>动态</span>
            <span>{item.count3}</span>
          </li>
        </ul>
      </div>
    );
  },
});

export default HierarchyHeader;
