.container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 9px 10px 9px 0;

  .companyInfo {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  ul {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 32px;

    li {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    li + li {
      position: relative;
      &::before {
        content: '';
        width: 0px;
        height: 22px;
        border: 1px solid #d8d8d8;
        position: absolute;
        top: 50%;
        transform: translate(-1px, -50%);
        left: -16px;
      }
    }
  }
}
