import { computed, defineComponent } from 'vue';
import SearchResult from '../../../trends/widgets/search-result';
import { useSearchFilter } from '../../../trends/hooks/use-search-filter';
import { SEARCH_RESULT_TABLE_COLUMNS } from '../../../trends/config/search-config';
import CommonSearchFilter from '@/components/common/common-search-filter';
import { openRelatinCourtModal } from '@/shared/components/related-court-drawer';
import { convertSortStructure } from '@/utils/data-type/convert-sort-structure';

const RelatedTrends = defineComponent({
  name: 'RelatedTrends',
  props: {},
  setup() {
    const {
      handleFilterChange,
      handleFilterReset,
      getFilterOptions,
      search,
      filterGroups,
      filterValues,
      isLoading,
      totalCompany,
      dataSource,
      pagination,
      isInit,
      sortInfo,
    } = useSearchFilter();

    /** 表格配置 */
    const tableColumns = computed(() => {
      return SEARCH_RESULT_TABLE_COLUMNS;
    });

    return {
      filterValues,
      filterGroups,
      handleFilterChange,
      handleFilterReset,
      getFilterOptions,
      search,
      isLoading,
      isInit,
      dataSource,
      pagination,
      sortInfo,

      tableColumns,
      totalCompany,
    };
  },
  render() {
    return (
      <div>
        <CommonSearchFilter
          placeholder="请输入企业名称"
          isRemoteSearch={true}
          filterConfig={this.filterGroups}
          onChange={this.handleFilterChange}
          defaultValue={this.filterValues}
          onReset={this.handleFilterReset}
        />

        {/* 搜索结果 */}
        <SearchResult
          totalCompany={this.totalCompany}
          isLoading={this.isLoading}
          rowKey="uniqueHashkey"
          searchKey={this.filterValues.keywords}
          columns={this.tableColumns}
          dataSource={this.dataSource}
          pagination={this.pagination}
          on={{
            changePage: (pageIndex: number, pageSize: number) => {
              this.search({ pageIndex, pageSize });
            },
            refresh: () => {
              this.search();
            },
            openCourtNotice: (list: any[]) => {
              openRelatinCourtModal({ data: list });
            },
            sorterChange: (sorter) => {
              this.pagination.current = 1;
              this.sortInfo = convertSortStructure(sorter);
              this.search();
            },
          }}
        />
      </div>
    );
  },
});

export default RelatedTrends;
