import { defineComponent } from 'vue';
import Hierarchy from '../../widgets/hierarchy-view';
import testData from './test-data.json';
import Header from './widgets/hierarchy-header';
import SubjectTrends from './widgets/subject-trends';
import RelatedTrends from './widgets/related-trends';

const TrendsHierarchy = defineComponent({
  name: 'TrendsHierarchy',
  props: {},
  render() {
    return (
      <div>
        <Hierarchy.View
          list={testData.data}
          scopedSlots={{
            header: (record) => {
              return <Header item={record} />;
            },
            body: (record) => {
              return (
                <div>
                  <Hierarchy.Wrapper>
                    <SubjectTrends />
                  </Hierarchy.Wrapper>
                  <Hierarchy.Wrapper>
                    <RelatedTrends />
                  </Hierarchy.Wrapper>
                </div>
              );
            },
          }}
        ></Hierarchy.View>
      </div>
    );
  },
});

export default TrendsHierarchy;
