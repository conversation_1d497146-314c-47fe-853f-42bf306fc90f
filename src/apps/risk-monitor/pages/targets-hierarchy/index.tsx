import { defineComponent } from 'vue';
import Hierarchy from '../../widgets/hierarchy-view';
import testData from './test-data.json';

const TargetsHierarchy = defineComponent({
  name: 'TargetsHierarchy',
  props: {},
  render() {
    return (
      <div>
        <Hierarchy.View
          list={testData.data}
          selectable
          rowKey="monitorCompanyId"
          scopedSlots={{
            header: (record) => {
              return <div>111</div>;
            },
            body: (record) => {
              return (
                <Hierarchy.Wrapper>
                  <div class="h-[300px]">333</div>
                </Hierarchy.Wrapper>
              );
            },
          }}
        ></Hierarchy.View>
      </div>
    );
  },
});

export default TargetsHierarchy;
